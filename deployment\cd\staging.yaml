# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

steps:
  - name: "python:3.11-slim" 
    id: install-dependencies
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        pip install uv==0.6.12 --user && uv sync --frozen
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

  - name: "python:3.11-slim"
    id: deploy-staging
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        # Install gcloud CLI for secret access
        apt-get update && apt-get install -y curl gnupg
        echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
        curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
        apt-get update && apt-get install -y google-cloud-cli

        # Retrieve secrets from Secret Manager
        echo "🔐 Retrieving secrets from Secret Manager..."
        DB_PASSWORD=$(gcloud secrets versions access latest --secret="my-agent-db-password" --project=${_STAGING_PROJECT_ID})
        TWILIO_ACCOUNT_SID=$(gcloud secrets versions access latest --secret="my-agent-twilio-account-sid" --project=${_STAGING_PROJECT_ID})
        TWILIO_AUTH_TOKEN=$(gcloud secrets versions access latest --secret="my-agent-twilio-auth-token" --project=${_STAGING_PROJECT_ID})
        TWILIO_WHATSAPP_NUMBER=$(gcloud secrets versions access latest --secret="my-agent-twilio-whatsapp-number" --project=${_STAGING_PROJECT_ID})
        ODOO_API_BASE_URL=$(gcloud secrets versions access latest --secret="my-agent-odoo-api-base-url" --project=${_STAGING_PROJECT_ID})
        ODOO_API_KEY=$(gcloud secrets versions access latest --secret="my-agent-odoo-api-key" --project=${_STAGING_PROJECT_ID})

        # Get database connection details
        DB_HOST=$(gcloud sql instances describe my-agent-postgres-staging --project=${_STAGING_PROJECT_ID} --format="value(ipAddresses[0].ipAddress)")
        DATABASE_URL="postgresql://my-agent-app-user:$${DB_PASSWORD}@$${DB_HOST}:5432/beauty_center"

        # Export requirements
        uv export --no-hashes --no-sources --no-header --no-dev --no-emit-project --no-annotate --frozen > .requirements.txt

        # Deploy to Agent Engine with all environment variables
        echo "🚀 Deploying to Agent Engine with environment variables..."
        uv run app/agent_engine_app.py \
          --project ${_STAGING_PROJECT_ID} \
          --location ${_REGION} \
          --set-env-vars="COMMIT_SHA=${COMMIT_SHA},DATABASE_URL=$${DATABASE_URL},TWILIO_ACCOUNT_SID=$${TWILIO_ACCOUNT_SID},TWILIO_AUTH_TOKEN=$${TWILIO_AUTH_TOKEN},TWILIO_WHATSAPP_NUMBER=$${TWILIO_WHATSAPP_NUMBER},ODOO_API_BASE_URL=$${ODOO_API_BASE_URL},ODOO_API_KEY=$${ODOO_API_KEY},ENVIRONMENT=staging,ALLOWED_ORIGINS=https://staging.yourdomain.com"
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'


  - name: gcr.io/cloud-builders/gcloud
    id: fetch-auth-token
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        echo $(gcloud auth print-access-token -q) > auth_token.txt

  # Load Testing
  - name: "python:3.11-slim"
    id: load_test
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        export _AUTH_TOKEN=$(cat auth_token.txt)
        uv add locust==2.32.6
        uv run locust -f tests/load_test/load_test.py \
        --headless \
        -t 30s -u 2 -r 0.5 \
        --csv=tests/load_test/.results/results \
        --html=tests/load_test/.results/report.html
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

  # Export Load Test Results to GCS
  - name: gcr.io/cloud-builders/gcloud
    id: export-results-to-gcs
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        export _TIMESTAMP=$(date +%Y%m%d-%H%M%S)
        gsutil -m cp -r tests/load_test/.results gs://${_BUCKET_NAME_LOAD_TEST_RESULTS}/results-$${_TIMESTAMP}
        echo "_________________________________________________________________________"
        echo "Load test results copied to gs://${_BUCKET_NAME_LOAD_TEST_RESULTS}/results-$${_TIMESTAMP}"
        echo "HTTP link: https://console.cloud.google.com/storage/browser/${_BUCKET_NAME_LOAD_TEST_RESULTS}/results-$${_TIMESTAMP}"
        echo "_________________________________________________________________________"

  # Trigger Prod Deployment
  - name: gcr.io/cloud-builders/gcloud
    id: trigger-prod-deployment
    entrypoint: gcloud
    args:
      - "beta"
      - "builds"
      - "triggers"
      - "run"
      - "deploy-my-agent"
      - "--region"
      - "$LOCATION"
      - "--project"
      - "$PROJECT_ID"
      - "--sha"
      - $COMMIT_SHA

  - name: gcr.io/cloud-builders/gcloud
    id: echo-view-build-trigger-link
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        echo "_________________________________________________________________________"
        echo "Production deployment triggered. View progress and / or approve on the Cloud Build Console:"
        echo "https://console.cloud.google.com/cloud-build/builds;region=$LOCATION"
        echo "_________________________________________________________________________"

substitutions:
  _STAGING_PROJECT_ID: YOUR_STAGING_PROJECT_ID
  _REGION: us-central1

logsBucket: gs://${PROJECT_ID}-my-agent-logs-data/build-logs
options:
  substitutionOption: ALLOW_LOOSE
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
